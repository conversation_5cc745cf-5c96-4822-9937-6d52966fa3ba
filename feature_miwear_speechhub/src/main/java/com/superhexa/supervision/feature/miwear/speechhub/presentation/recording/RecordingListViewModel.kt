package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording

import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.compose.runtime.MutableState
import androidx.core.net.toUri
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.RequestRecord
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.RequestRecord.RecordModel
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.MiWearRecordHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.MiWearRecordHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.RecordData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.RecordErrorCode.NOT_WORN
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.RecordErrorCode.RECORD_SUCCESS
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.RecordStatus
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.helper.AudioPlayerController
import com.superhexa.supervision.feature.miwear.speechhub.presentation.translate.observer.RecordObserver
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.db.AudioTranscriptionDbHelper
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.DeleteFileEvent
import com.superhexa.supervision.library.db.bean.AudioTranscriptionBean
import com.superhexa.supervision.library.db.bean.MediaBean
import com.xiaomi.wear.protobuf.nano.MediaProtos
import io.objectbox.reactive.DataSubscription
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap

/**
 * 类描述:
 * 创建日期: 2025/2/18 on 19:14
 * 作者: qintaiyuan
 */
class RecordingListViewModel :
    BaseMVIViewModel<RecordListState, RecordListEffect, RecordListEvent>() {
    private val _effect = MutableSharedFlow<RecordListEffect>(replay = 1)
    val effect: SharedFlow<RecordListEffect> = _effect
    private val _isRecording = MutableSharedFlow<Boolean>()
    val isRecording: SharedFlow<Boolean> = _isRecording
    private val controller = AudioPlayerController(LibBaseApplication.instance)
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val curMediaBean = MutableLiveData<MediaBean>(null)

    private var dataSubscription: DataSubscription? = null
    val mutableList = MutableLiveData<MutableList<MediaBean>>()
    private var audioDataSubscription: DataSubscription? = null
    private val _audioMutableList = MutableStateFlow<List<AudioTranscriptionBean>>(emptyList())
    val audioMutableList: StateFlow<List<AudioTranscriptionBean>> = _audioMutableList
    private var recordObserver: RecordObserver? = null
    private val decorator by lazy {
        if (DeviceModelManager.isMijiaO95SeriesDevice(bondDevice?.model)) {
            DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice)
        } else {
            null
        }
    }
    val deviceStateLiveData by lazy { decorator?.liveData }
    private val _deviceConnectState = MutableSharedFlow<Boolean>()
    val deviceConnectState: SharedFlow<Boolean> = _deviceConnectState
    private var recordingTime: Int = 0
    private var recordStartTime: Long = 0L
    private val _recordTimeText = MutableStateFlow(DateTimeUtils.formatTime(0L))
    val recordTimeText = _recordTimeText
    private var recordStatus = RecordStatus.RECORD_STOP

    // 录音处理状态管理
    private val processStatusMap = ConcurrentHashMap<String, String>() // filePath -> status
    private val _processStatusFlow = MutableStateFlow<Map<String, String>>(emptyMap())
    val processStatusFlow: StateFlow<Map<String, String>> = _processStatusFlow
    private val timeHandler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                UPDATE_RECORD_TIME -> {
                    if (msg.obj is Boolean && msg.obj == true) {
                        Timber.i("需要更新下时间 recordTime:$recordingTime")
                        recordStartTime = System.currentTimeMillis() - recordingTime * ONE_SECOND
                    }
                    _recordTimeText.value = DateTimeUtils.formatTime(System.currentTimeMillis() - recordStartTime)
                    sendEmptyMessageDelayed(UPDATE_RECORD_TIME, ONE_SECOND)
                }
            }
        }
    }

    override fun initUiState() = RecordListState()

    override fun reduce(oldState: RecordListState, event: RecordListEvent) {
        Timber.i("reduce $event")
        when (event) {
            is RecordListEvent.PlayOrPause -> handlePlayOrPause(event.bean, event.audioBean)
            is RecordListEvent.SeekTo -> {
                val seekTime = (event.progress * controller.duration.value).toLong()
                controller.seekTo(seekTime)
            }

            is RecordListEvent.Stop -> controller.stop()
            is RecordListEvent.ShowDeleteDialog -> showDeleteDialog()
            is RecordListEvent.ShowRenameDialog -> showRenameDialog(event.bean)
            is RecordListEvent.ShareItem -> shareItem(event.bean)
            is RecordListEvent.Init -> {
                recordObserver = RecordObserver(event.lifecycleOwner)
                addRecordStateObserver()
            }
        }
    }

    private fun showDeleteDialog() {
        viewModelScope.launch {
            _effect.emit(RecordListEffect.ShowDeleteDialog(getUniqueId()))
        }
    }

    private fun shareItem(bean: MediaBean) {
        viewModelScope.launch {
            _effect.emit(RecordListEffect.ShareItem(bean, getUniqueId()))
        }
    }

    private fun showRenameDialog(bean: MediaBean) {
        viewModelScope.launch {
            _effect.emit(RecordListEffect.ShowRenameDialog(bean, getUniqueId()))
        }
    }

    private fun getUniqueId(): Int {
        return System.currentTimeMillis().toInt()
    }

    // 使用 MutableStateFlow 存储 RecordData 的列表
    private val _pcmData = MutableStateFlow<List<RecordData>>(emptyList())
    val pcmData: StateFlow<List<RecordData>> = _pcmData

    init {
        // 监听 MiWearRecordHandler 中的 pcmDataCallback
        loadO95CompleteAudioList()
        // 监听播放器状态变化
        viewModelScope.launch {
            controller.playbackState.collect { state ->
                when (state) {
                    is AudioPlayerController.PlaybackState.PLAYING -> setState(
                        mState.value.copy(
                            isPlaying = true,
                            playStatus = PlayState.PLAY
                        )
                    )

                    is AudioPlayerController.PlaybackState.PAUSED ->
                        setState(
                            mState.value.copy(
                                isPlaying = false,
                                playStatus = PlayState.PAUSE
                            )
                        )

                    is AudioPlayerController.PlaybackState.STOPPED -> {
                        setState(
                            mState.value.copy(isPlaying = false, playStatus = PlayState.STOP)
                        )
                        controller.stop()
                    }

                    else -> {}
                }
            }
        }

        viewModelScope.launch {
            controller.currentPosition.collect { position ->
                // 太早获取文件时长会返回负数，这里当开始播放后再获取一次
                controller.getDuration()
                setState(mState.value.copy(playbackProgress = position))
            }
        }

        // 监听总时长
        viewModelScope.launch {
            controller.duration.collect { duration ->
                Timber.d("callback duration $duration")
                setState(mState.value.copy(totalDuration = duration))
            }
        }
    }

    private fun handlePlayOrPause(bean: MediaBean, audioBean: AudioTranscriptionBean) {
        val currentState = mState.value
        val isSamePath = currentState.currentPlayingPath == bean.path

        if (isSamePath) {
            when (currentState.playStatus) {
                PlayState.PLAY -> {
                    controller.pause()
                    Timber.i("handlePlayOrPause: pause")
                }

                PlayState.PAUSE -> {
                    controller.play(viewModelScope)
                    Timber.i("handlePlayOrPause: play")
                }

                else -> {
                    playNewMedia(bean, audioBean)
                    Timber.i("handlePlayOrPause: state:${currentState.playStatus}")
                }
            }
        } else {
            playNewMedia(bean, audioBean)
        }
    }

    private fun playNewMedia(bean: MediaBean, audioBean: AudioTranscriptionBean) {
        if (bean.path == null) {
            Timber.i("playNewMedia invalid path $bean")
            return
        }
        toUpdateIsFirstShow(audioBean)
        controller.stop()
        controller.prepare(bean.path.toUri())
        controller.play(viewModelScope)
        setState(mState.value.copy(currentPlayingPath = bean.path))
        Timber.i("playNewMedia: $bean")
    }

    fun toUpdateIsFirstShow(bean: AudioTranscriptionBean) = viewModelScope.launch {
        Timber.i("toUpdateIsFirstShow:  curMediaBean: isFirstShow ${bean.isFirstShow}")
        if (bean.isFirstShow) {
            AudioTranscriptionDbHelper.updateIsFirstShow(bean)
        }
    }

    fun toDeleteMediaFile(isDeleteGallery: Boolean = false) = viewModelScope.launch {
        val curMediaBean = curMediaBean.value ?: return@launch
        Timber.i("toDeleteMediaFile isDeleteGallery:$isDeleteGallery ,curMediaBean: $curMediaBean")
        EventBus.getDefault().post(DeleteFileEvent(isDeleteGallery, curMediaBean))
    }

    fun toRenameMediaFile(fileName: String) = viewModelScope.launch {
        val curMediaBean = curMediaBean.value ?: return@launch
        curMediaBean.fileName = fileName
        Timber.i("toRenameMediaFile: $fileName , curMediaBean: $curMediaBean")
        DbHelper.updateMediaFileName(curMediaBean)
    }

    fun stopPlay() {
        Timber.i("stopPlay")
        controller.stop()
    }

    fun resetPlayStatus() {
        Timber.d("resetPlayStatus called")
        controller.stop(false)
        setState(mState.value.copy(playbackProgress = 0))
    }

    fun startFace2f() = viewModelScope.launch {
        val requestRecord = MiWearRecordHandler.requestRecord(
            RequestRecord.RecordType.RecordTranslActionFace,
            RecordModel.StartRecord,
            decorator
        )
        Timber.tag(TAG).d("startFace2f---result:$requestRecord")
        if (requestRecord == null) {
            LibBaseApplication.instance.toast("发起录音失败")
            return@launch
        }
        // 0-OK 1-录音类型不支持 2-存储空间不足
        // 3-Conflict(跟其他场景冲突) 4-当前没有处于录音状态
        processCode(requestRecord)
    }

    fun stopRecord() = viewModelScope.launch {
        val requestRecord = MiWearRecordHandler.requestRecord(
            RequestRecord.RecordType.RecordTranslActionFace,
            RecordModel.StopRecord,
            decorator
        )
        Timber.tag(TAG).d("stopRecord---result:$requestRecord")
        if (requestRecord == null) {
            LibBaseApplication.instance.toast("停止录音失败")
            return@launch
        }
        // 0-OK 1-录音类型不支持 2-存储空间不足
        // 3-Conflict(跟其他场景冲突) 4-当前没有处于录音状态
        val code = requestRecord.code
        Timber.tag(TAG).i("stopRecord---result:$code")
        if (code != 0) {
            LibBaseApplication.instance.toast("停止录音失败code:$code")
        }
    }

    suspend fun deviceConnected(): Boolean = withContext(Dispatchers.Default) {
        val connected = decorator?.isChannelSuccess() == true
        _deviceConnectState.emit(connected)
        connected
    }

    fun startNormal(isCanRecord: MutableState<Boolean>) = viewModelScope.launch {
        //  / 0-正在录音 1-停止/结束 2-分段暂停
        val requestRecord = MiWearRecordHandler.requestRecord(
            RequestRecord.RecordType.RecordNormal,
            RecordModel.StartRecord,
            decorator
        )
        Timber.tag(TAG).d("startNormal---result:$requestRecord")
        if (requestRecord == null) {
            LibBaseApplication.instance.toast("发起录音失败")
            isCanRecord.value = false
            return@launch
        }
        // 0-OK 1-录音类型不支持 2-存储空间不足
        // 3-Conflict(跟其他场景冲突) 4-当前没有处于录音状态
        processCode(requestRecord, isCanRecord)
    }

    @Suppress("MagicNumber")
    private fun processCode(
        requestRecord: MediaProtos.RecordResponse,
        isCanRecord: MutableState<Boolean>? = null
    ) {
        Timber.i("processCode ${requestRecord.code}")
        when (val code = requestRecord.code) {
            RECORD_SUCCESS -> {
                updateRecordStatus(RecordStatus.RECORD_ING)
            }

            else -> {
                if (code == NOT_WORN) {
                    LibBaseApplication.instance.toast(R.string.record_bt_disconnect_tip)
                } else {
                    LibBaseApplication.instance.toast(MiWearRecordHelper.recordErrorMsg(code))
                }
                isCanRecord?.value = false
            }
        }
    }

    fun stopNormalRecord() = viewModelScope.launch {
        val requestRecord = MiWearRecordHandler.requestRecord(
            RequestRecord.RecordType.RecordNormal,
            RecordModel.StopRecord,
            decorator
        )
        Timber.tag(TAG).d("stopRecord---result:$requestRecord")
        if (requestRecord == null) {
            LibBaseApplication.instance.toast("停止录音失败")
            return@launch
        }
        // 0-OK 1-录音类型不支持 2-存储空间不足
        // 3-Conflict(跟其他场景冲突) 4-当前没有处于录音状态
        val code = requestRecord.code
        Timber.tag(TAG).i("stopNormalRecord $code")
        if (code == 0) {
            updateRecordStatus(RecordStatus.RECORD_STOP)
        } else {
            LibBaseApplication.instance.toast("停止录音失败code:$code")
        }
        getRecordState()
    }

    private fun loadO95CompleteAudioList() {
        dataSubscription?.cancel()
        audioDataSubscription?.cancel()

        viewModelScope.launch {
            dataSubscription = DbHelper.getO95CompleteAudioListFromDb { list ->
                try {
                    // 更新 LiveData
                    Timber.i("loadO95CompleteAudioList size：${list.size}")
                    list.forEachIndexed { index, item ->
                        Timber.d("MediaBean[$index]: path=${item.path}, id=${item.id}, fileName=${item.fileName}")
                    }
                    if (list.isNotNullOrEmpty()) {
                        Timber.i("开始调用saveMediaLis，userID: ${list.first().useId}")
                        AudioTranscriptionDbHelper.saveMediaLis(list, list.first().useId)
                        Timber.i("saveMediaLis调用完成")
                    }
                    viewModelScope.launch(Dispatchers.Main) {
                        Timber.i("更新mutableList.value，size: ${list.size}")
                        mutableList.value = list.toMutableList()
                        Timber.i("mutableList.value更新完成，当前size: ${mutableList.value?.size}")
                    }
                } catch (e: Exception) {
                    Timber.e(e, "loadO95CompleteAudioList 回调执行异常")
                    // 即使出现异常，也要更新UI
                    viewModelScope.launch(Dispatchers.Main) {
                        Timber.i("异常情况下更新mutableList.value，size: ${list.size}")
                        mutableList.value = list.toMutableList()
                    }
                }
            }
        }

        viewModelScope.launch {
            audioDataSubscription = AudioTranscriptionDbHelper.getO95AudioListFromDb { list ->
                try {
                    Timber.i("loadO95AudioList size：${list.size}")
                    list.forEachIndexed { index, item ->
                        Timber.d("AudioTranscriptionBean[$index]: path=${item.path}, id=${item.id}")
                    }
                    viewModelScope.launch(Dispatchers.Main) {
                        Timber.i("更新_audioMutableList.value，size: ${list.size}")
                        _audioMutableList.value = list.toList()
                        Timber.i("_audioMutableList.value更新完成，当前size: ${_audioMutableList.value.size}")
                    }
                } catch (e: Exception) {
                    Timber.e(e, "loadO95AudioList 回调执行异常")
                }
            }
        }
    }

    fun getRecordState() = viewModelScope.launch {
        if (!deviceConnected()) return@launch
        val recordStatus = MiWearRecordHandler.getRecordStatus(decorator)
        Timber.tag(TAG).d("getRecordState---result:$recordStatus")
        if (recordStatus == null) {
            LibBaseApplication.instance.toast("获取录音状态失败")
            return@launch
        }
        /**
         *     message ExtraData {
         *         required MediaFile media_file = 1;
         *         // 0-正在录音 1-停止/结束 2-分段暂停
         *         required uint32 status = 2 [(nanopb).int_size = IS_8];
         *     }
         *
         *     // 单位: 秒
         *     required LimitValue duration = 1;
         *     // 单位: 字节
         *     optional LimitValue size = 2;
         *     optional ExtraData extra_data = 3;
         */
        Timber.i("当前录音状态extraData:${recordStatus.extraData}")
        recordingTime = recordStatus.extraData?.mediaFile?.duration ?: 0
        updateRecordStatus(recordStatus.extraData?.status)
    }

    private fun updateRecordStatus(status: Int?) {
        Timber.i("updateRecordStatus: status $status")

        viewModelScope.launch {
            if (status == RecordStatus.RECORD_STOP) {
                recordingTime = 0
                recordStartTime = 0L
                recordStatus = RecordStatus.RECORD_STOP
                timeHandler.removeCallbacksAndMessages(null)
                _isRecording.emit(false)
            } else if (status == RecordStatus.RECORD_ING) {
                recordStatus = RecordStatus.RECORD_ING
                withContext(Dispatchers.Main) {
                    stopPlay()
                }
                startTimer()
                _isRecording.emit(true)
            }
        }
    }

    @Suppress("MagicNumber")
    @Synchronized
    private fun startTimer() {
        timeHandler.removeMessages(UPDATE_RECORD_TIME)
        val msg = Message.obtain()
        msg.what = UPDATE_RECORD_TIME
        msg.obj = true
        timeHandler.sendMessage(msg)
    }

    @Suppress("EmptyIfBlock")
    private fun addRecordStateObserver() {
        Timber.i("addRecordStateObserver")
        recordObserver?.addRecordObserve(
            decorator = decorator,
            onDeviceConnectState = { isConnect ->
                Timber.i("onDeviceConnectState:$isConnect")
                if (!isConnect) {
                    updateRecordStatus(RecordStatus.RECORD_STOP)
                } else {
                    getRecordState()
                }
            },
            onCameraJointState = { isIn ->
                Timber.i("onCameraJointState:$isIn")
            },
            onDeviceWearingState = { isWear ->
                Timber.i("onDeviceWearingState:$isWear")
            },
            onRecordState = { status ->
                Timber.i("onRecordState:$status")
                updateRecordStatus(status)
            }
        )
    }

    private fun removeRecordStateObserver() {
        Timber.i("addRecordStateObserver")
        // 建议不需要显示调用，内部通过lifecycleOwner管理.
        recordObserver?.removeRecordObserve()
    }

    /**
     * 更新录音处理状态
     * @param filePath 音频文件路径
     * @param status 处理状态
     */
    fun updateProcessStatus(filePath: String, status: String) {
        processStatusMap[filePath] = status
        _processStatusFlow.value = processStatusMap.toMap()
        Timber.i("更新录音处理状态: filePath=$filePath, status=$status")
    }

    /**
     * 获取指定文件的处理状态
     * @param filePath 音频文件路径
     * @return 处理状态，如果没有记录则返回null
     */
    fun getProcessStatus(filePath: String): String? {
        return processStatusMap[filePath]
    }

    /**
     * 清除指定文件的处理状态
     * @param filePath 音频文件路径
     */
    fun clearProcessStatus(filePath: String) {
        processStatusMap.remove(filePath)
        _processStatusFlow.value = processStatusMap.toMap()
        Timber.i("清除录音处理状态: filePath=$filePath")
    }

    override fun onCleared() {
        super.onCleared()
        // 取消订阅，避免内存泄漏
        removeRecordStateObserver()
        controller.release()
        audioDataSubscription?.cancel()
        dataSubscription?.cancel()
    }

    companion object {
        private const val TAG = "RecordingListViewModel"
        private const val UPDATE_RECORD_TIME = 0x111
        private const val ONE_SECOND = 1000L
    }
}
